<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Admin Panel</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="screen">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <h1>TEAM-<span class="accent">AP</span></h1>
                    <p class="login-subtitle">ADMIN PANEL</p>
                </div>
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username">USERNAME</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">PASSWORD</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="login-btn">ACCESS PANEL</button>
                </form>
                <div id="loginError" class="error-message"></div>
            </div>
        </div>
    </div>

    <!-- Dashboard Screen -->
    <div id="dashboardScreen" class="screen hidden">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>TEAM-<span class="accent">AP</span></h1>
                    <span class="nav-subtitle">ADMIN PANEL</span>
                </div>
                <div class="nav-actions">
                    <span id="welcomeUser" class="welcome-text"></span>
                    <button id="logoutBtn" class="logout-btn">LOGOUT</button>
                </div>
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>

        <div class="admin-content">
            <div class="container">
                <div class="admin-tabs">
                    <button class="admin-tab-btn active" data-tab="tournaments">TOURNAMENTS</button>
                    <button class="admin-tab-btn" data-tab="teams">TEAMS</button>
                    <button class="admin-tab-btn" data-tab="matches">MATCHES</button>
                    <button class="admin-tab-btn" data-tab="brackets">BRACKETS</button>
                </div>

                <!-- Tournaments Tab -->
                <div id="tournaments" class="admin-tab-content active">
                    <div class="admin-section-header">
                        <h2 class="section-title">TOURNAMENTS</h2>
                        <div class="section-actions">
                            <button id="refreshTournamentsBtn" class="admin-btn admin-btn-secondary">REFRESH</button>
                            <button id="addTournamentBtn" class="admin-btn admin-btn-primary">ADD TOURNAMENT</button>
                        </div>
                    </div>
                    <div id="tournamentsList" class="admin-data-grid"></div>
                </div>

                <!-- Teams Tab -->
                <div id="teams" class="admin-tab-content">
                    <div class="admin-section-header">
                        <h2 class="section-title">TEAMS</h2>
                        <div class="section-controls">
                            <select id="teamTournamentSelect" class="admin-select">
                                <option value="">SELECT TOURNAMENT</option>
                            </select>
                            <button id="addTeamBtn" class="admin-btn admin-btn-primary">ADD TEAM</button>
                        </div>
                    </div>
                    <div id="teamsList" class="admin-data-grid"></div>
                </div>

                <!-- Matches Tab -->
                <div id="matches" class="admin-tab-content">
                    <div class="admin-section-header">
                        <h2 class="section-title">MATCHES</h2>
                        <div class="section-controls">
                            <select id="matchTournamentSelect" class="admin-select">
                                <option value="">SELECT TOURNAMENT</option>
                            </select>
                            <button id="addMatchBtn" class="admin-btn admin-btn-primary">ADD MATCH</button>
                        </div>
                    </div>
                    <div id="matchesList" class="admin-data-grid"></div>
                </div>

                <!-- Brackets Tab -->
                <div id="brackets" class="admin-tab-content">
                    <div class="admin-section-header">
                        <h2 class="section-title">BRACKETS</h2>
                        <div class="section-controls">
                            <select id="bracketTournamentSelect" class="admin-select">
                                <option value="">SELECT TOURNAMENT</option>
                            </select>
                            <button id="addBracketBtn" class="admin-btn admin-btn-primary">ADD BRACKET</button>
                        </div>
                    </div>
                    <div id="bracketsList" class="admin-data-grid"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for forms -->
    <div id="modal" class="admin-modal">
        <div class="admin-modal-content">
            <div class="admin-modal-header">
                <h3 id="modalTitle" class="modal-title"></h3>
                <span class="admin-modal-close">&times;</span>
            </div>
            <div id="modalBody" class="admin-modal-body"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
