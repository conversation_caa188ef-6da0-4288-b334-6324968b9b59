<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Management - Admin Dashboard</title>
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>Admin Dashboard</span>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="matches.html" class="nav-link active">Matches</a>
                    </li>
                    <li class="nav-item">
                        <a href="stats.html" class="nav-link">Statistics</a>
                    </li>
                </ul>
                <div class="admin-info">
                    <span id="admin-status">Admin Panel</span>
                    <button id="logout-btn" class="btn btn-danger btn-sm">Logout</button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Admin Login</h2>
            </div>
            <div class="modal-body">
                <div class="login-form">
                    <div class="input-group">
                        <label for="admin-token">Admin Token:</label>
                        <input type="password" id="admin-token" placeholder="Enter admin token">
                    </div>
                    <button id="login-btn" class="btn btn-primary">Login</button>
                    <div id="login-error" class="error" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <main class="main-content" id="admin-content" style="display: none;">
        <div class="container">
            <section class="dashboard-header">
                <h1>🎮 Match Management</h1>
                <p>Comprehensive match control and monitoring</p>
            </section>

            <div class="dashboard-grid">
                <!-- Filters Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-filter"></i> Filters</h3>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <label for="status-filter">Filter by Status:</label>
                            <select id="status-filter">
                                <option value="">All Matches</option>
                                <option value="created">Created</option>
                                <option value="open">Open for Predictions</option>
                                <option value="closed">Predictions Closed</option>
                                <option value="finished">Finished</option>
                            </select>
                        </div>
                        <button id="apply-filter" class="btn btn-primary">Apply Filter</button>
                        <button id="clear-filter" class="btn btn-secondary">Clear</button>
                    </div>
                </div>

                <!-- Quick Stats Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> Quick Stats</h3>
                    </div>
                    <div class="card-body">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value" id="total-matches">0</div>
                                <div class="stat-label">Total Matches</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="active-matches">0</div>
                                <div class="stat-label">Active</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="finished-matches">0</div>
                                <div class="stat-label">Finished</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Matches Card -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> All Matches</h3>
                        <div class="header-actions">
                            <button id="refresh-matches" class="btn btn-primary btn-sm">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button id="auto-refresh-toggle" class="btn btn-secondary btn-sm">
                                <i class="fas fa-pause"></i> Auto-refresh: ON
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="loading-matches" class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading matches...</span>
                        </div>
                        <div id="matches-container">
                            <!-- Matches will be loaded here -->
                        </div>
                        <div id="no-matches" class="no-data" style="display: none;">
                            <i class="fas fa-calendar-times"></i>
                            <p>No matches found.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Valorant Tournament Predictions - Admin Panel</p>
        </div>
    </footer>

    <script src="js/matches.js"></script>
</body>
</html>
