// Configuration
const API_BASE_URL = 'http://localhost:5000/api'; // Local development URL

// Global variables
let currentUser = null;
let matches = [];
let countdownIntervals = {};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeUser();
    loadMatches();
    setupEventListeners();
});

// User management
function initializeUser() {
    const savedUser = localStorage.getItem('valorant_user');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateUserDisplay();
        loadUserStats();
    } else {
        showUserSetup();
    }
}

function showUserSetup() {
    document.getElementById('user-setup').style.display = 'block';
}

function saveUser() {
    const username = document.getElementById('username-input').value.trim();
    if (!username) {
        alert('Please enter a username');
        return;
    }

    // Generate a unique user ID
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    currentUser = {
        id: userId,
        username: username,
        points: 0
    };

    localStorage.setItem('valorant_user', JSON.stringify(currentUser));
    document.getElementById('user-setup').style.display = 'none';
    updateUserDisplay();
    loadUserStats();
}

function updateUserDisplay() {
    if (currentUser) {
        document.getElementById('user-points').textContent = `Points: ${currentUser.points}`;
        document.getElementById('user-id-display').textContent = currentUser.username;
    }
}

// Load user statistics
async function loadUserStats() {
    if (!currentUser) return;

    try {
        const response = await fetch(`${API_BASE_URL}/user/${currentUser.id}`);
        if (response.ok) {
            const data = await response.json();
            if (data.user) {
                currentUser.points = data.user.points;
                updateUserDisplay();
                // Update localStorage
                localStorage.setItem('valorant_user', JSON.stringify(currentUser));
            }
        }
    } catch (error) {
        console.error('Error loading user stats:', error);
    }
}

// Match management
async function loadMatches() {
    const loadingElement = document.getElementById('loading');
    const matchesContainer = document.getElementById('matches-container');
    const noMatchesElement = document.getElementById('no-matches');

    try {
        loadingElement.style.display = 'block';
        matchesContainer.innerHTML = '';
        noMatchesElement.style.display = 'none';

        const response = await fetch(`${API_BASE_URL}/matches`);
        if (!response.ok) {
            throw new Error('Failed to load matches');
        }

        const data = await response.json();
        matches = data.matches || [];

        loadingElement.style.display = 'none';

        if (matches.length === 0) {
            noMatchesElement.style.display = 'block';
        } else {
            displayMatches();
        }
    } catch (error) {
        console.error('Error loading matches:', error);
        loadingElement.style.display = 'none';
        matchesContainer.innerHTML = '<div class="error">Failed to load matches. Please try again later.</div>';
    }
}

function displayMatches() {
    const container = document.getElementById('matches-container');
    container.innerHTML = '';

    matches.forEach(match => {
        const matchCard = createMatchCard(match);
        container.appendChild(matchCard);
    });
}

function createMatchCard(match) {
    const card = document.createElement('div');
    card.className = 'match-card';
    card.innerHTML = `
        <div class="match-header">
            <span class="match-status status-${match.status}">${match.status}</span>
            <span class="match-id">#${match._id.slice(-6)}</span>
        </div>
        <div class="teams">
            <div class="team">
                <div class="team-name">${match.teamA}</div>
            </div>
            <div class="vs">VS</div>
            <div class="team">
                <div class="team-name">${match.teamB}</div>
            </div>
        </div>
        ${match.status === 'open' ? `
            <div class="countdown" id="countdown-${match._id}"></div>
            <div class="match-actions">
                <button class="btn btn-primary" onclick="openPredictionModal('${match._id}')">
                    <i class="fas fa-trophy"></i> Make Prediction
                </button>
            </div>
        ` : match.status === 'finished' && match.winner ? `
            <div class="match-result">
                <strong>Winner: ${match.winner}</strong>
            </div>
        ` : ''}
    `;

    // Start countdown if match is open
    if (match.status === 'open' && match.prediction_end_time) {
        startCountdown(match._id, match.prediction_end_time);
    }

    return card;
}

function startCountdown(matchId, endTime) {
    const countdownElement = document.getElementById(`countdown-${matchId}`);
    if (!countdownElement) return;

    const endDate = new Date(endTime);

    countdownIntervals[matchId] = setInterval(() => {
        const now = new Date();
        const timeLeft = endDate - now;

        if (timeLeft <= 0) {
            countdownElement.textContent = 'Predictions closed';
            clearInterval(countdownIntervals[matchId]);
            // Reload matches to update status
            setTimeout(loadMatches, 1000);
            return;
        }

        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

// Prediction modal
function openPredictionModal(matchId) {
    if (!currentUser) {
        alert('Please set up your username first');
        showUserSetup();
        return;
    }

    const match = matches.find(m => m._id === matchId);
    if (!match) return;

    const modal = document.getElementById('prediction-modal');
    const teamAElement = document.getElementById('modal-team-a');
    const teamBElement = document.getElementById('modal-team-b');

    teamAElement.querySelector('.team-name').textContent = match.teamA;
    teamBElement.querySelector('.team-name').textContent = match.teamB;

    // Set up prediction buttons
    teamAElement.querySelector('.predict-btn').onclick = () => makePrediction(matchId, match.teamA);
    teamBElement.querySelector('.predict-btn').onclick = () => makePrediction(matchId, match.teamB);

    // Start modal countdown
    if (match.prediction_end_time) {
        startModalCountdown(match.prediction_end_time);
    }

    modal.style.display = 'block';
}

function startModalCountdown(endTime) {
    const countdownElement = document.getElementById('modal-countdown');
    const endDate = new Date(endTime);

    const updateCountdown = () => {
        const now = new Date();
        const timeLeft = endDate - now;

        if (timeLeft <= 0) {
            countdownElement.textContent = 'Predictions closed';
            return;
        }

        const minutes = Math.floor(timeLeft / 60000);
        const seconds = Math.floor((timeLeft % 60000) / 1000);
        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    // Clear interval when modal is closed
    const modal = document.getElementById('prediction-modal');
    const observer = new MutationObserver(() => {
        if (modal.style.display === 'none') {
            clearInterval(interval);
            observer.disconnect();
        }
    });
    observer.observe(modal, { attributes: true, attributeFilter: ['style'] });
}

async function makePrediction(matchId, selectedTeam) {
    if (!currentUser) {
        alert('Please set up your username first');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/predict`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: currentUser.id,
                match_id: matchId,
                selected_team: selectedTeam
            })
        });

        const data = await response.json();

        if (response.ok) {
            alert(`Prediction submitted successfully! You predicted: ${selectedTeam}`);
            closeModal();
            loadMatches(); // Refresh matches
        } else {
            alert(data.error || 'Failed to submit prediction');
        }
    } catch (error) {
        console.error('Error making prediction:', error);
        alert('Failed to submit prediction. Please try again.');
    }
}

function closeModal() {
    document.getElementById('prediction-modal').style.display = 'none';
}

// Event listeners
function setupEventListeners() {
    // User setup
    document.getElementById('save-username').addEventListener('click', saveUser);
    document.getElementById('username-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            saveUser();
        }
    });

    // Modal close
    document.querySelector('.close').addEventListener('click', closeModal);
    window.addEventListener('click', function(e) {
        const modal = document.getElementById('prediction-modal');
        if (e.target === modal) {
            closeModal();
        }
    });

    // Auto-refresh matches every 30 seconds
    setInterval(loadMatches, 30000);
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}
