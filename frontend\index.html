<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Valorant Tournament Predictions</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>VALORANT <span class="accent">PREDICTIONS</span></h1>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link active">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="leaderboard.html" class="nav-link">Leaderboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="profile.html" class="nav-link">Profile</a>
                    </li>
                </ul>
                <div class="user-info">
                    <span id="user-points">Points: 0</span>
                    <span id="user-id-display"></span>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="container">
            <section class="hero">
                <div class="hero-content">
                    <h1>PREDICT <span class="accent">TOURNAMENT</span> WINNERS</h1>
                    <p>Make your predictions and climb the leaderboard</p>
                </div>
            </section>

            <section class="user-setup" id="auth-section">
                <div class="setup-card">
                    <h2 id="auth-title">Welcome to Valorant Predictions</h2>

                    <!-- Login Form -->
                    <div id="login-form" class="auth-form">
                        <div class="input-group">
                            <input type="email" id="login-email" placeholder="Email Address" required>
                        </div>
                        <div class="input-group">
                            <input type="password" id="login-password" placeholder="Password" required>
                        </div>
                        <div class="input-group">
                            <button id="login-btn" class="btn btn-primary">Login</button>
                        </div>
                        <p class="auth-switch">
                            Don't have an account?
                            <a href="#" id="show-register">Register here</a>
                        </p>
                    </div>

                    <!-- Registration Form -->
                    <div id="register-form" class="auth-form" style="display: none;">
                        <div class="input-group">
                            <input type="text" id="register-fullname" placeholder="Full Name" required>
                        </div>
                        <div class="input-group">
                            <input type="email" id="register-email" placeholder="Email Address" required>
                        </div>
                        <div class="input-group">
                            <input type="text" id="register-ingame" placeholder="In-Game Name (Valorant)" required>
                        </div>
                        <div class="input-group">
                            <input type="password" id="register-password" placeholder="Password" required>
                        </div>
                        <div class="input-group">
                            <button id="register-btn" class="btn btn-primary">Register</button>
                        </div>
                        <p class="auth-switch">
                            Already have an account?
                            <a href="#" id="show-login">Login here</a>
                        </p>
                    </div>
                </div>
            </section>

            <section class="matches-section">
                <h2 class="section-title">ACTIVE MATCHES</h2>
                <div id="loading" class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Loading matches...</span>
                </div>
                <div id="matches-container" class="matches-grid">
                    <!-- Matches will be loaded here -->
                </div>
                <div id="no-matches" class="no-matches" style="display: none;">
                    <i class="fas fa-calendar-times"></i>
                    <h3>No active matches</h3>
                    <p>Check back later for upcoming tournaments!</p>
                </div>
            </section>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Valorant Tournament Predictions. All rights reserved.</p>
        </div>
    </footer>

    <!-- Prediction Modal -->
    <div id="prediction-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-header">
                <h2>Make Your Prediction</h2>
            </div>
            <div class="modal-body">
                <div class="match-info">
                    <div class="teams">
                        <div class="team team-a" id="modal-team-a">
                            <div class="team-name"></div>
                            <button class="predict-btn" data-team="a">
                                <i class="fas fa-trophy"></i>
                                Predict Win
                            </button>
                        </div>
                        <div class="vs">VS</div>
                        <div class="team team-b" id="modal-team-b">
                            <div class="team-name"></div>
                            <button class="predict-btn" data-team="b">
                                <i class="fas fa-trophy"></i>
                                Predict Win
                            </button>
                        </div>
                    </div>
                </div>
                <div class="countdown-info">
                    <p>Prediction window closes in:</p>
                    <div id="modal-countdown" class="countdown"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
