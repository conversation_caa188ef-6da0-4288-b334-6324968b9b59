// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Change this to your deployed backend URL

// Global state
let currentTournament = null;
let teams = [];
let matches = [];
let currentFilter = 'all';

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadData();
});

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
    
    // Filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            // Update active filter
            document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Update current filter and display matches
            currentFilter = btn.dataset.filter;
            displayMatches();
        });
    });
}

// API helper function
async function apiCall(endpoint) {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`API call failed: ${response.status} ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.error('API call error:', error);
        return null;
    }
}

// Load all data
async function loadData() {
    await loadCurrentTournament();
    if (currentTournament) {
        await loadTeams();
        await loadMatches();
        displayTournamentInfo();
        displayMatches();
    } else {
        await loadMostRecentTournament();
    }
}

// Load current live tournament
async function loadCurrentTournament() {
    currentTournament = await apiCall('/api/tournaments/live');
}

// Load most recent completed tournament if no live tournament
async function loadMostRecentTournament() {
    const completedTournaments = await apiCall('/api/tournaments/completed');
    if (completedTournaments && completedTournaments.length > 0) {
        currentTournament = completedTournaments.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
        )[0];
        
        await loadTeams();
        await loadMatches();
        displayTournamentInfo();
        displayMatches();
    } else {
        displayNoTournament();
    }
}

// Load teams for current tournament
async function loadTeams() {
    if (currentTournament) {
        teams = await apiCall(`/api/tournaments/${currentTournament.id}/teams`) || [];
    }
}

// Load matches for current tournament
async function loadMatches() {
    if (currentTournament) {
        matches = await apiCall(`/api/tournaments/${currentTournament.id}/matches`) || [];
        // Sort matches by start time (earliest to latest)
        matches.sort((a, b) => new Date(a.start_time) - new Date(b.start_time));
    }
}

// Display tournament information
function displayTournamentInfo() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    
    if (!currentTournament) {
        displayNoTournament();
        return;
    }
    
    const statusText = currentTournament.status === 'live' ? 'LIVE NOW' : 
                      currentTournament.status === 'upcoming' ? 'UPCOMING' : 'COMPLETED';
    
    tournamentInfoElement.innerHTML = `
        <div class="tournament-info-content">
            <h2 class="tournament-name">${currentTournament.name}</h2>
            <div class="tournament-details">
                <span class="tournament-type">${currentTournament.type}</span>
                <span class="tournament-status ${currentTournament.status}">${statusText}</span>
                ${currentTournament.prize_pool ? `<span class="tournament-prize">₹${currentTournament.prize_pool.toLocaleString()}</span>` : ''}
            </div>
            ${currentTournament.notes ? `
                <div class="tournament-notes">
                    <div class="notes-header">📋 Tournament Notes</div>
                    <div class="notes-content">${currentTournament.notes}</div>
                </div>
            ` : ''}
        </div>
    `;
}

// Helper function to get rank image path
function getRankImagePath(filename) {
    return filename ? `rank_png/${filename}` : '';
}

// Helper function to create player list with ranks
function createPlayerList(team, maxPlayers = 3) {
    if (!team) return '';

    let players = [];
    if (team.players && team.players.length > 0) {
        players = team.players;
    } else if (team.members && team.members.length > 0) {
        players = team.members.map(member => ({ name: member, rank: null }));
    }

    if (players.length === 0) return '';

    const displayPlayers = players.slice(0, maxPlayers);
    const remainingCount = players.length - maxPlayers;

    let playerList = displayPlayers.map(player => {
        const rankImg = player.rank ?
            `<img src="${getRankImagePath(player.rank)}" alt="Rank" class="rank-icon">` : '';
        return `${rankImg}${player.name}`;
    }).join(', ');

    if (remainingCount > 0) {
        playerList += ` +${remainingCount} more`;
    }

    return playerList;
}

// Display matches based on current filter
function displayMatches() {
    const matchesListElement = document.getElementById('matchesList');

    if (!matches || matches.length === 0) {
        matchesListElement.innerHTML = '<div class="no-data">No matches found for this tournament.</div>';
        return;
    }

    // Filter matches based on current filter
    let filteredMatches = matches;
    if (currentFilter !== 'all') {
        filteredMatches = matches.filter(match => match.status === currentFilter);
    }

    // Apply chronological sorting based on filter type
    filteredMatches = sortMatchesByFilter(filteredMatches, currentFilter);

    if (filteredMatches.length === 0) {
        matchesListElement.innerHTML = `<div class="no-data">No ${currentFilter} matches found.</div>`;
        return;
    }

    // Create table structure
    const tableHTML = `
        <div class="schedule-table-container">
            <table class="schedule-table">
                <thead>
                    <tr>
                        <th>Match</th>
                        <th>Team A</th>
                        <th>Score</th>
                        <th>Team B</th>
                        <th>Date & Time</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    ${filteredMatches.map(match => {
                        const teamA = getTeamById(match.teamA);
                        const teamB = getTeamById(match.teamB);
                        const winner = match.winner ? getTeamById(match.winner) : null;

                        return `
                            <tr class="schedule-row ${match.status}">
                                <td class="match-id">
                                    <span class="match-number">Match ${match.match_id || match.id}</span>
                                    ${match.round ? `<span class="match-round">Round ${match.round}</span>` : ''}
                                </td>
                                <td class="team-cell ${winner && winner.id === teamA?.id ? 'winner' : ''}">
                                    <div class="team-info">
                                        <span class="team-initials">${teamA ? teamA.initials : '??'}</span>
                                        <span class="team-name">${teamA ? teamA.name : 'TBD'}</span>
                                    </div>
                                </td>
                                <td class="score-cell">
                                    <div class="score-display">
                                        <span class="score-a">${match.status === 'completed' && match.scoreA !== null ? match.scoreA : '-'}</span>
                                        <span class="score-separator">:</span>
                                        <span class="score-b">${match.status === 'completed' && match.scoreB !== null ? match.scoreB : '-'}</span>
                                    </div>
                                </td>
                                <td class="team-cell ${winner && winner.id === teamB?.id ? 'winner' : ''}">
                                    <div class="team-info">
                                        <span class="team-initials">${teamB ? teamB.initials : '??'}</span>
                                        <span class="team-name">${teamB ? teamB.name : 'TBD'}</span>
                                    </div>
                                </td>
                                <td class="time-cell">
                                    <span class="match-time">${formatMatchTime(match.start_time)}</span>
                                </td>
                                <td class="status-cell">
                                    <span class="status-badge ${match.status}">${match.status.toUpperCase()}</span>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;

    matchesListElement.innerHTML = tableHTML;
}

// Display no tournament message
function displayNoTournament() {
    const tournamentInfoElement = document.getElementById('tournamentInfo');
    const matchesListElement = document.getElementById('matchesList');
    
    const noTournamentMessage = `
        <div class="no-tournament">
            <h2>No Tournament Currently Available</h2>
            <p>Check back soon for upcoming tournaments!</p>
        </div>
    `;
    
    if (tournamentInfoElement) {
        tournamentInfoElement.innerHTML = noTournamentMessage;
    }
    
    if (matchesListElement) {
        matchesListElement.innerHTML = '<div class="no-data">No matches available.</div>';
    }
}

// Helper function to get team by ID
function getTeamById(teamId) {
    return teams.find(team => team.id === teamId);
}

// Utility function to convert to IST timezone
function toISTDate(dateString) {
    const date = new Date(dateString);
    // Convert to IST (UTC+5:30)
    const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
    return new Date(utc + istOffset);
}

// Sort matches based on filter type with proper chronological order
function sortMatchesByFilter(matches, filter) {
    switch (filter) {
        case 'upcoming':
        case 'live':
            // For upcoming and live matches, show earliest first
            return matches.sort((a, b) => new Date(a.start_time) - new Date(b.start_time));
        case 'completed':
            // For completed matches, show most recent first
            return matches.sort((a, b) => {
                const timeA = new Date(a.completed_at || a.start_time);
                const timeB = new Date(b.completed_at || b.start_time);
                return timeB - timeA; // Most recent first
            });
        case 'all':
        default:
            // For all matches, show chronological order (earliest to latest)
            return matches.sort((a, b) => new Date(a.start_time) - new Date(b.start_time));
    }
}

// Format match time for display with IST timezone
function formatMatchTime(dateString) {
    const date = toISTDate(dateString);
    const now = new Date();
    const diffMs = date - now;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffMs < 0) {
        // Past match
        return date.toLocaleDateString('en-IN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Kolkata'
        });
    } else if (diffHours < 24) {
        // Today
        return `Today ${date.toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Kolkata'
        })}`;
    } else if (diffDays < 7) {
        // This week
        return date.toLocaleDateString('en-IN', {
            weekday: 'short',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Kolkata'
        });
    } else {
        // Future
        return date.toLocaleDateString('en-IN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Kolkata'
        });
    }
}
