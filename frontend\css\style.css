/* Import Valorant-style fonts */
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap');

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
    line-height: 1.6;
    color: #ece8e1;
    background: #0f1419;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 70, 85, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 70, 85, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0f1419 0%, #1e2328 50%, #0f1419 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Valorant-style background pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, transparent 40%, rgba(255, 70, 85, 0.02) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(255, 70, 85, 0.02) 50%, transparent 60%);
    background-size: 60px 60px;
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #0f1419 0%, #1e2328 100%);
    border-bottom: 2px solid #ff4655;
    box-shadow:
        0 4px 20px rgba(255, 70, 85, 0.3),
        inset 0 1px 0 rgba(255, 70, 85, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #ff4655, transparent);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    position: relative;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 700;
    color: #ff4655;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
}

.nav-logo i {
    margin-right: 0.75rem;
    font-size: 2rem;
    filter: drop-shadow(0 0 8px rgba(255, 70, 85, 0.6));
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-link {
    text-decoration: none;
    color: #ece8e1;
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1), rgba(255, 70, 85, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    clip-path: inherit;
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 1;
}

.nav-link:hover,
.nav-link.active {
    color: #ff4655;
    border-color: #ff4655;
    text-shadow: 0 0 8px rgba(255, 70, 85, 0.6);
    transform: translateY(-2px);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 1rem;
    color: #ece8e1;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Main content */
.main-content {
    padding: 3rem 0;
    min-height: calc(100vh - 120px);
    position: relative;
}

.hero {
    text-align: center;
    margin-bottom: 4rem;
    color: #ece8e1;
    position: relative;
}

.hero h1 {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 3px;
    background: linear-gradient(135deg, #ff4655, #ff6b7a, #ff4655);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(255, 70, 85, 0.5);
    position: relative;
}

.hero h1::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ff4655, #ff6b7a);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: blur(2px);
    opacity: 0.7;
    z-index: -1;
}

.hero p {
    font-size: 1.4rem;
    font-weight: 500;
    opacity: 0.9;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
}

/* User setup */
.user-setup {
    margin-bottom: 4rem;
}

.setup-card {
    background: linear-gradient(135deg, #1e2328, #0f1419);
    border: 2px solid #ff4655;
    padding: 3rem;
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
    box-shadow:
        0 15px 35px rgba(255, 70, 85, 0.2),
        inset 0 1px 0 rgba(255, 70, 85, 0.1);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.setup-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 40%, rgba(255, 70, 85, 0.05) 50%, transparent 60%);
    pointer-events: none;
}

.setup-card h2 {
    font-family: 'Orbitron', monospace;
    color: #ff4655;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.input-group {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.input-group input {
    flex: 1;
    padding: 1rem;
    background: rgba(15, 20, 25, 0.8);
    border: 2px solid #ff4655;
    color: #ece8e1;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 500;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
    transition: all 0.3s ease;
}

.input-group input::placeholder {
    color: rgba(236, 232, 225, 0.6);
}

.input-group input:focus {
    outline: none;
    border-color: #ff6b7a;
    box-shadow: 0 0 15px rgba(255, 70, 85, 0.3);
    background: rgba(15, 20, 25, 0.9);
}

/* Buttons */
.btn {
    padding: 1rem 2rem;
    border: 2px solid #ff4655;
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1), rgba(255, 70, 85, 0.05));
    color: #ff4655;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 100%, 15px 100%);
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 70, 85, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #ff4655, #ff6b7a);
    color: #0f1419;
    border-color: #ff4655;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff6b7a, #ff4655);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
    text-shadow: 0 0 8px rgba(15, 20, 25, 0.8);
}

.btn-success {
    background: linear-gradient(135deg, #00d4aa, #00f5d4);
    color: #0f1419;
    border-color: #00d4aa;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #00f5d4, #00d4aa);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff4655, #ff1744);
    color: #ece8e1;
    border-color: #ff4655;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff1744, #ff4655);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
}

/* Matches section */
.matches-section h2 {
    color: #ece8e1;
    text-align: center;
    margin-bottom: 3rem;
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 20px rgba(255, 70, 85, 0.5);
}

.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2.5rem;
}

.match-card {
    background: linear-gradient(135deg, #1e2328, #0f1419);
    border: 2px solid #ff4655;
    padding: 2rem;
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
    box-shadow:
        0 15px 35px rgba(255, 70, 85, 0.2),
        inset 0 1px 0 rgba(255, 70, 85, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 40%, rgba(255, 70, 85, 0.05) 50%, transparent 60%);
    pointer-events: none;
}

.match-card:hover {
    transform: translateY(-8px);
    box-shadow:
        0 20px 40px rgba(255, 70, 85, 0.3),
        inset 0 1px 0 rgba(255, 70, 85, 0.2);
    border-color: #ff6b7a;
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.match-status {
    padding: 0.5rem 1rem;
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 8px 100%);
    position: relative;
}

.status-open {
    background: linear-gradient(135deg, #00d4aa, #00f5d4);
    color: #0f1419;
    box-shadow: 0 0 15px rgba(0, 212, 170, 0.4);
}

.status-closed {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
    color: #0f1419;
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.4);
}

.status-finished {
    background: linear-gradient(135deg, #2196f3, #64b5f6);
    color: #0f1419;
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.4);
}

.status-created {
    background: linear-gradient(135deg, #9c27b0, #ba68c8);
    color: #ece8e1;
    box-shadow: 0 0 15px rgba(156, 39, 176, 0.4);
}

.teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 2rem 0;
    position: relative;
}

.team {
    text-align: center;
    flex: 1;
    position: relative;
}

.team-name {
    font-family: 'Orbitron', monospace;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ece8e1;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.vs {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 900;
    color: #ff4655;
    margin: 0 1.5rem;
    text-shadow: 0 0 15px rgba(255, 70, 85, 0.6);
    position: relative;
}

.vs::before,
.vs::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff4655, transparent);
}

.vs::before {
    left: -40px;
}

.vs::after {
    right: -40px;
}

.countdown {
    text-align: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    color: #ff4655;
    font-weight: 700;
    background: rgba(255, 70, 85, 0.1);
    padding: 1rem;
    border: 1px solid #ff4655;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
}

.predict-btn {
    background: linear-gradient(135deg, #ff4655, #ff6b7a);
    color: #0f1419;
    border: 2px solid #ff4655;
    padding: 0.75rem 1.5rem;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 8px 100%);
    position: relative;
    overflow: hidden;
}

.predict-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.predict-btn:hover::before {
    left: 100%;
}

.predict-btn:hover {
    background: linear-gradient(135deg, #ff6b7a, #ff4655);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 70, 85, 0.4);
}

.predict-btn:disabled {
    background: linear-gradient(135deg, #666, #888);
    color: #ccc;
    border-color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading and empty states */
.loading {
    text-align: center;
    color: #ece8e1;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    padding: 2rem;
}

.loading i {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
    color: #ff4655;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.no-matches {
    text-align: center;
    color: #ece8e1;
    padding: 4rem;
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1), rgba(255, 70, 85, 0.05));
    border: 1px solid rgba(255, 70, 85, 0.3);
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
}

.no-matches i {
    font-size: 5rem;
    margin-bottom: 2rem;
    color: #ff4655;
    opacity: 0.7;
}

.no-matches h3 {
    font-family: 'Orbitron', monospace;
    color: #ff4655;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 20, 25, 0.9);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #1e2328, #0f1419);
    border: 2px solid #ff4655;
    margin: 5% auto;
    padding: 0;
    clip-path: polygon(0 0, calc(100% - 30px) 0, 100% 100%, 30px 100%);
    width: 90%;
    max-width: 700px;
    box-shadow:
        0 25px 50px rgba(255, 70, 85, 0.3),
        inset 0 1px 0 rgba(255, 70, 85, 0.1);
    position: relative;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 40%, rgba(255, 70, 85, 0.05) 50%, transparent 60%);
    pointer-events: none;
}

.modal-header {
    padding: 2rem;
    border-bottom: 2px solid rgba(255, 70, 85, 0.3);
    text-align: center;
    position: relative;
}

.modal-header h2 {
    font-family: 'Orbitron', monospace;
    color: #ff4655;
    font-size: 2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin: 0;
}

.modal-body {
    padding: 2.5rem;
    color: #ece8e1;
}

.close {
    color: #ff4655;
    float: right;
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    padding: 1rem;
    transition: all 0.3s ease;
    position: absolute;
    top: 10px;
    right: 10px;
}

.close:hover {
    color: #ff6b7a;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.6);
    transform: scale(1.1);
}

.countdown-info {
    text-align: center;
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid #ff4655;
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 100%, 15px 100%);
}

.countdown-info p {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ece8e1;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #0f1419, #1e2328);
    border-top: 2px solid #ff4655;
    color: #ece8e1;
    text-align: center;
    padding: 2rem 0;
    margin-top: auto;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #ff4655, transparent);
}

footer p {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
    letter-spacing: 1px;
}

/* Leaderboard styles */
.leaderboard-section {
    max-width: 1200px;
    margin: 0 auto;
}

.leaderboard-container {
    background: linear-gradient(135deg, #1e2328, #0f1419);
    border: 2px solid #ff4655;
    clip-path: polygon(0 0, calc(100% - 30px) 0, 100% 100%, 30px 100%);
    padding: 3rem;
    box-shadow:
        0 20px 40px rgba(255, 70, 85, 0.2),
        inset 0 1px 0 rgba(255, 70, 85, 0.1);
    position: relative;
}

.leaderboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 40%, rgba(255, 70, 85, 0.05) 50%, transparent 60%);
    pointer-events: none;
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
}

.leaderboard-header h2 {
    font-family: 'Orbitron', monospace;
    color: #ff4655;
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin: 0;
    text-shadow: 0 0 15px rgba(255, 70, 85, 0.5);
}

.leaderboard-table {
    width: 100%;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 80px 1fr 100px 120px 100px;
    gap: 1rem;
    padding: 1.5rem;
    align-items: center;
}

.table-header {
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid #ff4655;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1rem;
    color: #ff4655;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.table-row {
    background: rgba(30, 35, 40, 0.5);
    border: 1px solid rgba(255, 70, 85, 0.2);
    clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 8px 100%);
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    color: #ece8e1;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
}

.table-row:hover {
    background: rgba(255, 70, 85, 0.1);
    border-color: #ff4655;
    transform: translateX(5px);
}

.table-row.current-user {
    background: #e3f2fd;
    border: 2px solid #2196f3;
    border-radius: 8px;
    margin: 0.25rem 0;
}

.table-row.rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.table-row.rank-2 {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
}

.table-row.rank-3 {
    background: linear-gradient(135deg, #cd7f32, #daa520);
}

.rank {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rank i.gold {
    color: #ffd700;
}

.rank i.silver {
    color: #c0c0c0;
}

.rank i.bronze {
    color: #cd7f32;
}

.you-badge {
    background: #2196f3;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.username {
    font-weight: 600;
}

.points strong {
    color: #667eea;
    font-size: 1.1rem;
}

/* Profile styles */
.profile-section {
    max-width: 800px;
    margin: 0 auto;
}

.profile-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.predictions-history {
    margin-top: 2rem;
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.prediction-match {
    flex: 1;
}

.prediction-result {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.result-correct {
    background: #d4edda;
    color: #155724;
}

.result-incorrect {
    background: #f8d7da;
    color: #721c24;
}

.result-pending {
    background: #fff3cd;
    color: #856404;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .matches-grid {
        grid-template-columns: 1fr;
    }

    .input-group {
        flex-direction: column;
    }

    .teams {
        flex-direction: column;
        gap: 1rem;
    }

    .vs {
        margin: 0;
    }

    .table-header,
    .table-row {
        grid-template-columns: 60px 1fr 80px 80px 80px;
        gap: 0.5rem;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .leaderboard-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .prediction-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}
