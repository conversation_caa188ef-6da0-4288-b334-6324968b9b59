/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.nav-logo i {
    margin-right: 0.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #667eea;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.9rem;
    color: #666;
}

/* Main content */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 120px);
}

.hero {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* User setup */
.user-setup {
    margin-bottom: 3rem;
}

.setup-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.input-group {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.input-group input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

/* Matches section */
.matches-section h2 {
    color: white;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.match-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.match-card:hover {
    transform: translateY(-5px);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.match-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-open {
    background: #d4edda;
    color: #155724;
}

.status-closed {
    background: #f8d7da;
    color: #721c24;
}

.status-finished {
    background: #d1ecf1;
    color: #0c5460;
}

.teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 1.5rem 0;
}

.team {
    text-align: center;
    flex: 1;
}

.team-name {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.vs {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
    margin: 0 1rem;
}

.countdown {
    text-align: center;
    font-size: 1.1rem;
    color: #667eea;
    font-weight: 600;
}

.predict-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.predict-btn:hover {
    background: #5a6fd8;
    transform: scale(1.05);
}

.predict-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Loading and empty states */
.loading {
    text-align: center;
    color: white;
    font-size: 1.2rem;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.no-matches {
    text-align: center;
    color: white;
    padding: 3rem;
}

.no-matches i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.modal-body {
    padding: 2rem;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    padding: 1rem;
}

.close:hover {
    color: #000;
}

.countdown-info {
    text-align: center;
    margin-top: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Footer */
footer {
    background: rgba(0, 0, 0, 0.1);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: auto;
}

/* Leaderboard styles */
.leaderboard-section {
    max-width: 1000px;
    margin: 0 auto;
}

.leaderboard-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.leaderboard-header h2 {
    color: #333;
    margin: 0;
}

.leaderboard-table {
    width: 100%;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 80px 1fr 100px 120px 100px;
    gap: 1rem;
    padding: 1rem;
    align-items: center;
}

.table-header {
    background: #f8f9fa;
    border-radius: 8px;
    font-weight: bold;
    color: #666;
    margin-bottom: 0.5rem;
}

.table-row {
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s ease;
}

.table-row:hover {
    background: #f8f9fa;
}

.table-row.current-user {
    background: #e3f2fd;
    border: 2px solid #2196f3;
    border-radius: 8px;
    margin: 0.25rem 0;
}

.table-row.rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.table-row.rank-2 {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
}

.table-row.rank-3 {
    background: linear-gradient(135deg, #cd7f32, #daa520);
}

.rank {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rank i.gold {
    color: #ffd700;
}

.rank i.silver {
    color: #c0c0c0;
}

.rank i.bronze {
    color: #cd7f32;
}

.you-badge {
    background: #2196f3;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.username {
    font-weight: 600;
}

.points strong {
    color: #667eea;
    font-size: 1.1rem;
}

/* Profile styles */
.profile-section {
    max-width: 800px;
    margin: 0 auto;
}

.profile-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.predictions-history {
    margin-top: 2rem;
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.prediction-match {
    flex: 1;
}

.prediction-result {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.result-correct {
    background: #d4edda;
    color: #155724;
}

.result-incorrect {
    background: #f8d7da;
    color: #721c24;
}

.result-pending {
    background: #fff3cd;
    color: #856404;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .matches-grid {
        grid-template-columns: 1fr;
    }

    .input-group {
        flex-direction: column;
    }

    .teams {
        flex-direction: column;
        gap: 1rem;
    }

    .vs {
        margin: 0;
    }

    .table-header,
    .table-row {
        grid-template-columns: 60px 1fr 80px 80px 80px;
        gap: 0.5rem;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .leaderboard-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .prediction-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}
