@echo off
echo ========================================
echo  Valorant Tournament Prediction Website
echo  Local Development Server
echo ========================================
echo.

echo [1/3] Installing Python dependencies...
cd backend
pip install -r requirements.txt
echo.

echo [2/3] Starting Flask backend server...
echo Backend will run on: http://localhost:5000
echo Admin token: admin123
echo.
start cmd /k "python app.py"

echo [3/3] Opening frontend in browser...
timeout /t 3 /nobreak > nul
cd ..\frontend
start index.html

echo.
echo ========================================
echo  Setup Complete!
echo ========================================
echo  Frontend: Open frontend/index.html in browser
echo  Admin Panel: Open admin/index.html in browser
echo  Backend API: http://localhost:5000
echo  Admin Token: admin123
echo ========================================
pause
