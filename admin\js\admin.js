o// Configuration
const API_BASE_URL = 'http://localhost:5000/api'; // Local development URL

// Global variables
let adminToken = null;
let matches = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    setupEventListeners();
});

// Authentication
function checkAdminAuth() {
    const savedToken = localStorage.getItem('admin_token');
    if (savedToken) {
        adminToken = savedToken;
        showAdminContent();
        loadMatches();
    } else {
        showLoginModal();
    }
}

function showLoginModal() {
    document.getElementById('login-modal').style.display = 'block';
    document.getElementById('admin-content').style.display = 'none';
}

function showAdminContent() {
    document.getElementById('login-modal').style.display = 'none';
    document.getElementById('admin-content').style.display = 'block';
}

async function login() {
    const username = document.getElementById('admin-username').value.trim();
    const password = document.getElementById('admin-password').value.trim();
    const errorElement = document.getElementById('login-error');

    if (!username || !password) {
        showError(errorElement, 'Please enter username and password');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            adminToken = data.token;
            localStorage.setItem('admin_token', data.token);
            showAdminContent();
            loadMatches();
            errorElement.style.display = 'none';
        } else {
            showError(errorElement, data.error || 'Invalid credentials');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError(errorElement, 'Failed to connect to server');
    }
}

function logout() {
    adminToken = null;
    localStorage.removeItem('admin_token');
    showLoginModal();
    document.getElementById('admin-username').value = '';
    document.getElementById('admin-password').value = '';
}

function showError(element, message) {
    element.textContent = message;
    element.style.display = 'block';
}

// Match management
async function createMatch() {
    const teamA = document.getElementById('team-a').value.trim();
    const teamB = document.getElementById('team-b').value.trim();
    const duration = parseInt(document.getElementById('prediction-duration').value);

    if (!teamA || !teamB) {
        alert('Please enter both team names');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/admin/create_match`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                teamA: teamA,
                teamB: teamB,
                prediction_duration_minutes: duration
            })
        });

        const data = await response.json();

        if (response.ok) {
            alert('Match created successfully!');
            document.getElementById('create-match-form').reset();
            loadMatches();
        } else {
            alert(data.error || 'Failed to create match');
        }
    } catch (error) {
        console.error('Error creating match:', error);
        alert('Failed to create match. Please try again.');
    }
}

async function loadMatches() {
    const loadingElement = document.getElementById('loading-matches');
    const containerElement = document.getElementById('matches-container');
    const noMatchesElement = document.getElementById('no-matches');

    try {
        loadingElement.style.display = 'block';
        containerElement.innerHTML = '';
        noMatchesElement.style.display = 'none';

        const response = await fetch(`${API_BASE_URL}/admin/matches`, {
            headers: {
                'Authorization': `Bearer ${adminToken}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to load matches');
        }

        const data = await response.json();
        matches = data.matches || [];

        loadingElement.style.display = 'none';

        if (matches.length === 0) {
            noMatchesElement.style.display = 'block';
        } else {
            displayMatches();
        }
    } catch (error) {
        console.error('Error loading matches:', error);
        loadingElement.style.display = 'none';
        containerElement.innerHTML = '<div class="error">Failed to load matches. Please try again later.</div>';
    }
}

function displayMatches() {
    const container = document.getElementById('matches-container');
    container.innerHTML = '';

    matches.forEach(match => {
        const matchCard = createMatchCard(match);
        container.appendChild(matchCard);
    });
}

function createMatchCard(match) {
    const card = document.createElement('div');
    card.className = 'match-card';
    
    const actionsHtml = getMatchActions(match);
    
    card.innerHTML = `
        <div class="match-header">
            <span class="match-status status-${match.status}">${match.status}</span>
            <span class="match-id">#${match._id.slice(-6)}</span>
        </div>
        <div class="match-teams">
            <h4>${match.teamA} vs ${match.teamB}</h4>
            ${match.winner ? `<p><strong>Winner: ${match.winner}</strong></p>` : ''}
        </div>
        <div class="match-info">
            <p><strong>Created:</strong> ${formatDate(match.created_at)}</p>
            ${match.start_time ? `<p><strong>Started:</strong> ${formatDate(match.start_time)}</p>` : ''}
            ${match.prediction_end_time ? `<p><strong>Predictions close:</strong> ${formatDate(match.prediction_end_time)}</p>` : ''}
            <p><strong>Prediction window:</strong> ${match.prediction_duration_minutes} minutes</p>
            ${match.total_predictions !== undefined ? `<p><strong>Total predictions:</strong> ${match.total_predictions}</p>` : ''}
        </div>
        <div class="match-actions">
            ${actionsHtml}
        </div>
    `;

    return card;
}

function getMatchActions(match) {
    const actions = [];

    switch (match.status) {
        case 'created':
            actions.push(`<button class="btn btn-success" onclick="startPrediction('${match._id}')">
                <i class="fas fa-play"></i> Start Predictions
            </button>`);
            break;
        case 'open':
            actions.push(`<button class="btn btn-warning" onclick="closePrediction('${match._id}')">
                <i class="fas fa-stop"></i> Close Predictions
            </button>`);
            break;
        case 'closed':
            actions.push(`<button class="btn btn-primary" onclick="setWinner('${match._id}', '${match.teamA}')">
                ${match.teamA} Wins
            </button>`);
            actions.push(`<button class="btn btn-primary" onclick="setWinner('${match._id}', '${match.teamB}')">
                ${match.teamB} Wins
            </button>`);
            break;
        case 'finished':
            actions.push(`<span class="btn btn-secondary" disabled>Match Completed</span>`);
            break;
    }

    return actions.join('');
}

async function startPrediction(matchId) {
    if (!confirm('Start prediction window for this match?')) return;

    try {
        const response = await fetch(`${API_BASE_URL}/admin/start_prediction`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({ match_id: matchId })
        });

        const data = await response.json();

        if (response.ok) {
            alert('Prediction started successfully!');
            loadMatches();
        } else {
            alert(data.error || 'Failed to start prediction');
        }
    } catch (error) {
        console.error('Error starting prediction:', error);
        alert('Failed to start prediction. Please try again.');
    }
}

async function closePrediction(matchId) {
    if (!confirm('Close prediction window for this match?')) return;

    try {
        const response = await fetch(`${API_BASE_URL}/admin/close_prediction`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({ match_id: matchId })
        });

        const data = await response.json();

        if (response.ok) {
            alert('Prediction closed successfully!');
            loadMatches();
        } else {
            alert(data.error || 'Failed to close prediction');
        }
    } catch (error) {
        console.error('Error closing prediction:', error);
        alert('Failed to close prediction. Please try again.');
    }
}

async function setWinner(matchId, winnerTeam) {
    if (!confirm(`Set ${winnerTeam} as the winner? This will award points to users who predicted correctly.`)) return;

    try {
        const response = await fetch(`${API_BASE_URL}/admin/set_winner`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                match_id: matchId,
                winner_team: winnerTeam
            })
        });

        const data = await response.json();

        if (response.ok) {
            alert(`Winner set successfully! Points awarded to ${data.points_awarded_to} users.`);
            loadMatches();
        } else {
            alert(data.error || 'Failed to set winner');
        }
    } catch (error) {
        console.error('Error setting winner:', error);
        alert('Failed to set winner. Please try again.');
    }
}

// Event listeners
function setupEventListeners() {
    // Login
    document.getElementById('login-btn').addEventListener('click', login);
    document.getElementById('admin-username').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('admin-password').focus();
        }
    });
    document.getElementById('admin-password').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            login();
        }
    });

    // Logout
    document.getElementById('logout-btn').addEventListener('click', logout);

    // Create match form
    document.getElementById('create-match-form').addEventListener('submit', function(e) {
        e.preventDefault();
        createMatch();
    });

    // Quick actions
    document.getElementById('refresh-matches').addEventListener('click', loadMatches);
    document.getElementById('load-matches').addEventListener('click', loadMatches);
    document.getElementById('view-leaderboard').addEventListener('click', function() {
        window.open('../frontend/leaderboard.html', '_blank');
    });
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}
