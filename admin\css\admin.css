/* Import Valorant-style fonts */
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap');

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
    line-height: 1.6;
    color: #ece8e1;
    background: #0f1419;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 70, 85, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 70, 85, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0f1419 0%, #1e2328 50%, #0f1419 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Valorant-style background pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, transparent 40%, rgba(255, 70, 85, 0.02) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(255, 70, 85, 0.02) 50%, transparent 60%);
    background-size: 60px 60px;
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #0f1419 0%, #1e2328 100%);
    border-bottom: 2px solid #ff4655;
    box-shadow:
        0 4px 20px rgba(255, 70, 85, 0.3),
        inset 0 1px 0 rgba(255, 70, 85, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #ff4655, transparent);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    position: relative;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 700;
    color: #ff4655;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
}

.nav-logo i {
    margin-right: 0.75rem;
    font-size: 2rem;
    filter: drop-shadow(0 0 8px rgba(255, 70, 85, 0.6));
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-link {
    text-decoration: none;
    color: #ece8e1;
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1), rgba(255, 70, 85, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    clip-path: inherit;
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 1;
}

.nav-link:hover,
.nav-link.active {
    color: #ff4655;
    border-color: #ff4655;
    text-shadow: 0 0 8px rgba(255, 70, 85, 0.6);
    transform: translateY(-2px);
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    color: #ece8e1;
    font-weight: 500;
}

/* Buttons */
.btn {
    padding: 1rem 2rem;
    border: 2px solid #ff4655;
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1), rgba(255, 70, 85, 0.05));
    color: #ff4655;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 100%, 15px 100%);
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 70, 85, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #ff4655, #ff6b7a);
    color: #0f1419;
    border-color: #ff4655;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff6b7a, #ff4655);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #666, #888);
    color: #ece8e1;
    border-color: #666;
    box-shadow: 0 4px 15px rgba(102, 102, 102, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #888, #666);
    transform: translateY(-3px);
}

.btn-success {
    background: linear-gradient(135deg, #00d4aa, #00f5d4);
    color: #0f1419;
    border-color: #00d4aa;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #00f5d4, #00d4aa);
    transform: translateY(-3px);
}

.btn-danger {
    background: linear-gradient(135deg, #ff4655, #ff1744);
    color: #ece8e1;
    border-color: #ff4655;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff1744, #ff4655);
    transform: translateY(-3px);
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
    color: #0f1419;
    border-color: #ff9800;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #ffb74d, #ff9800);
    transform: translateY(-3px);
}

.btn-info {
    background: linear-gradient(135deg, #2196f3, #64b5f6);
    color: #0f1419;
    border-color: #2196f3;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #64b5f6, #2196f3);
    transform: translateY(-3px);
}

/* Main content */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 120px);
}

.dashboard-header {
    text-align: center;
    margin-bottom: 4rem;
    color: #ece8e1;
    position: relative;
}

.dashboard-header h1 {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 3px;
    background: linear-gradient(135deg, #ff4655, #ff6b7a, #ff4655);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(255, 70, 85, 0.5);
}

.dashboard-header p {
    font-size: 1.4rem;
    font-weight: 500;
    opacity: 0.9;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: linear-gradient(135deg, #1e2328, #0f1419);
    border: 2px solid #ff4655;
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
    box-shadow:
        0 15px 35px rgba(255, 70, 85, 0.2),
        inset 0 1px 0 rgba(255, 70, 85, 0.1);
    overflow: hidden;
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 40%, rgba(255, 70, 85, 0.05) 50%, transparent 60%);
    pointer-events: none;
}

.dashboard-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    background: rgba(255, 70, 85, 0.1);
    padding: 2rem;
    border-bottom: 2px solid rgba(255, 70, 85, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.card-header h3 {
    margin: 0;
    font-family: 'Orbitron', monospace;
    color: #ff4655;
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card-header i {
    margin-right: 0.75rem;
    color: #ff4655;
    filter: drop-shadow(0 0 5px rgba(255, 70, 85, 0.5));
}

.card-body {
    padding: 2.5rem;
    color: #ece8e1;
}

/* Forms */
.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #3498db;
}

/* Quick actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Match cards */
.match-card {
    background: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.match-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.match-id {
    font-family: monospace;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.match-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-created {
    background: #d1ecf1;
    color: #0c5460;
}

.status-open {
    background: #d4edda;
    color: #155724;
}

.status-closed {
    background: #f8d7da;
    color: #721c24;
}

.status-finished {
    background: #d1ecf1;
    color: #0c5460;
}

.match-teams {
    text-align: center;
    margin: 1rem 0;
}

.match-teams h4 {
    font-size: 1.2rem;
    color: #333;
}

.match-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.match-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* Modal */
.modal {
    display: block;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.modal-body {
    padding: 2rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}



/* Loading and error states */
.loading {
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    padding: 2rem;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.no-data {
    text-align: center;
    color: #666;
    padding: 2rem;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.success {
    background: #d4edda;
    color: #155724;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
}

/* Footer */
footer {
    background: rgba(0, 0, 0, 0.1);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: auto;
}

/* Additional match card styles */
.match-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.match-title h4 {
    margin: 0;
    color: #333;
}

.match-details {
    margin: 1rem 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 600;
    color: #666;
}

.detail-row .value {
    color: #333;
}

.detail-row .value.winner {
    color: #27ae60;
    font-weight: bold;
}

.prediction-stats {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.prediction-stats h5 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.stats-row {
    margin-bottom: 0.5rem;
}

.team-predictions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.team-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team-name {
    font-weight: 600;
}

.prediction-count {
    color: #3498db;
    font-weight: bold;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .dashboard-header h1 {
        font-size: 2rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .match-actions {
        flex-direction: column;
    }

    .match-actions .btn {
        flex: none;
    }

    .admin-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .match-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .team-predictions {
        font-size: 0.9rem;
    }
}
