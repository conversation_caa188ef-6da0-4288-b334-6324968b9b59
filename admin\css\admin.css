/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #e74c3c;
}

.nav-logo i {
    margin-right: 0.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #e74c3c;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

/* Main content */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 120px);
}

.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.dashboard-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dashboard-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #333;
}

.card-header i {
    margin-right: 0.5rem;
    color: #3498db;
}

.card-body {
    padding: 2rem;
}

/* Forms */
.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #3498db;
}

/* Quick actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Match cards */
.match-card {
    background: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.match-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.match-id {
    font-family: monospace;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.match-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-created {
    background: #d1ecf1;
    color: #0c5460;
}

.status-open {
    background: #d4edda;
    color: #155724;
}

.status-closed {
    background: #f8d7da;
    color: #721c24;
}

.status-finished {
    background: #d1ecf1;
    color: #0c5460;
}

.match-teams {
    text-align: center;
    margin: 1rem 0;
}

.match-teams h4 {
    font-size: 1.2rem;
    color: #333;
}

.match-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.match-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* Modal */
.modal {
    display: block;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.modal-body {
    padding: 2rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.login-hint {
    text-align: center;
    margin-top: 0.5rem;
}

.login-hint small {
    color: #666;
    font-style: italic;
}

/* Loading and error states */
.loading {
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    padding: 2rem;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.no-data {
    text-align: center;
    color: #666;
    padding: 2rem;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.success {
    background: #d4edda;
    color: #155724;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
}

/* Footer */
footer {
    background: rgba(0, 0, 0, 0.1);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: auto;
}

/* Additional match card styles */
.match-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.match-title h4 {
    margin: 0;
    color: #333;
}

.match-details {
    margin: 1rem 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 600;
    color: #666;
}

.detail-row .value {
    color: #333;
}

.detail-row .value.winner {
    color: #27ae60;
    font-weight: bold;
}

.prediction-stats {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.prediction-stats h5 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.stats-row {
    margin-bottom: 0.5rem;
}

.team-predictions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.team-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team-name {
    font-weight: 600;
}

.prediction-count {
    color: #3498db;
    font-weight: bold;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .dashboard-header h1 {
        font-size: 2rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .match-actions {
        flex-direction: column;
    }

    .match-actions .btn {
        flex: none;
    }

    .admin-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .match-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .team-predictions {
        font-size: 0.9rem;
    }
}
