# 🚀 Local Development Setup

## Quick Start (Automated)

### Windows:
```bash
# Double-click or run in terminal
start_local.bat
```

### Mac/Linux:
```bash
# Make executable and run
chmod +x start_local.sh
./start_local.sh
```

## Manual Setup

### 1. Install Dependencies

```bash
# Navigate to backend folder
cd backend

# Install Python packages
pip install -r requirements.txt
```

### 2. Start Backend Server

```bash
# In the backend folder
python app.py
```

The backend will start on: **http://localhost:5000**

### 3. Open Frontend

Open these files in your web browser:

- **User Site:** `frontend/index.html`
- **Admin Panel:** `admin/index.html`

## 🔑 Admin Access

- **Admin Token:** `admin123`
- Use this token to log into the admin panel

## 📱 Testing the Application

### As a User:
1. Open `frontend/index.html`
2. Enter a username when prompted
3. Wait for admin to create matches
4. Make predictions on active matches
5. Check leaderboard and profile

### As an Admin:
1. Open `admin/index.html`
2. <PERSON>gin with token: `admin123`
3. Create a new match (e.g., "Team Liquid vs Fnatic")
4. Start predictions (5-minute window)
5. Close predictions
6. Set winner and award points

## 🔧 Configuration

### Database
- Using your existing MongoDB Atlas cluster
- Database name: `valorant_predictions`
- Connection configured in `backend/.env`

### API Endpoints
- All frontend JavaScript files configured for `http://localhost:5000/api`

## 🐛 Troubleshooting

### Backend Issues:
```bash
# Check if Python is installed
python --version

# Check if pip is working
pip --version

# Install dependencies manually
pip install Flask Flask-CORS pymongo python-dotenv
```

### Frontend Issues:
- Make sure backend is running first
- Check browser console for errors (F12)
- Try refreshing the page

### Database Issues:
- Verify MongoDB Atlas connection string in `backend/.env`
- Check if your IP is whitelisted in MongoDB Atlas
- Test connection: the backend will show connection status on startup

## 📊 Sample Workflow

1. **Start backend:** `python app.py` in backend folder
2. **Open admin panel:** `admin/index.html` → Login with `admin123`
3. **Create match:** "Sentinels vs 100 Thieves", 5 minutes prediction window
4. **Start predictions:** Click "Start Predictions" button
5. **Open user site:** `frontend/index.html` → Enter username
6. **Make prediction:** Choose a team before countdown ends
7. **Close predictions:** In admin panel, click "Close Predictions"
8. **Set winner:** Click the winning team button
9. **Check results:** View leaderboard and user profile

## 🌐 URLs

- **Backend API:** http://localhost:5000
- **Health Check:** http://localhost:5000/health
- **Frontend:** Open `frontend/index.html` in browser
- **Admin Panel:** Open `admin/index.html` in browser

## 📝 Notes

- Backend runs on port 5000
- Frontend files are static HTML - just open in browser
- Admin token is hardcoded as `admin123` for local development
- Database uses your existing MongoDB Atlas cluster
- All CORS issues are handled by Flask-CORS
