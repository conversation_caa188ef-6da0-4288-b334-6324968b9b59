<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Login</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin: 10px 0; }
        input, button { padding: 10px; margin: 5px; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Test Admin Login</h1>
    
    <div class="form-group">
        <label>Username:</label>
        <input type="text" id="username" value="admin">
    </div>
    
    <div class="form-group">
        <label>Password:</label>
        <input type="password" id="password" value="admin123">
    </div>
    
    <button onclick="testLogin()">Test Login</button>
    
    <div id="result" class="result"></div>

    <script>
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = 'Testing login...';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3>Response:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Data:</strong> ${JSON.stringify(data, null, 2)}</p>
                `;
                
                if (response.ok) {
                    resultDiv.style.backgroundColor = '#d4edda';
                    resultDiv.style.color = '#155724';
                } else {
                    resultDiv.style.backgroundColor = '#f8d7da';
                    resultDiv.style.color = '#721c24';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
                resultDiv.style.backgroundColor = '#f8d7da';
                resultDiv.style.color = '#721c24';
            }
        }
    </script>
</body>
</html>
