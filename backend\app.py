from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
from models import db, Match, Prediction, User
from functools import wraps

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')

# Hardcoded admin credentials
ADMIN_USERNAME = 'admin'
ADMIN_PASSWORD = 'admin123'

# Initialize database
db.init_app(app)

def admin_required(f):
    """Decorator to require admin authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'Unauthorized'}), 403

        token = auth_header.split(' ')[1]
        # Simple token validation - in production, use proper JWT
        if token != 'admin_session_token':
            return jsonify({'error': 'Unauthorized'}), 403
        return f(*args, **kwargs)
    return decorated_function

# Public API Routes

@app.route('/api/matches', methods=['GET'])
def get_matches():
    """Get all matches with their current status"""
    try:
        matches = Match.get_all_matches()
        return jsonify({'matches': matches}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/predict', methods=['POST'])
def make_prediction():
    """Submit a user prediction"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        match_id = data.get('match_id')
        selected_team = data.get('selected_team')
        
        if not all([user_id, match_id, selected_team]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Check if match exists and predictions are open
        match = Match.get_match_by_id(match_id)
        if not match:
            return jsonify({'error': 'Match not found'}), 404
        
        if match['status'] != 'open':
            return jsonify({'error': 'Predictions are closed for this match'}), 400
        
        # Check if prediction window is still open
        if datetime.utcnow() > match['prediction_end_time']:
            return jsonify({'error': 'Prediction window has closed'}), 400
        
        # Check if user already predicted for this match
        existing_prediction = Prediction.get_user_prediction(user_id, match_id)
        if existing_prediction:
            return jsonify({'error': 'You have already predicted for this match'}), 400
        
        # Create prediction
        prediction_id = Prediction.create_prediction(user_id, match_id, selected_team)
        
        # Ensure user exists
        User.ensure_user_exists(user_id)
        
        return jsonify({
            'message': 'Prediction submitted successfully',
            'prediction_id': prediction_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/leaderboard', methods=['GET'])
def get_leaderboard():
    """Get the global leaderboard"""
    try:
        leaderboard = User.get_leaderboard()
        return jsonify({'leaderboard': leaderboard}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/user/<user_id>', methods=['GET'])
def get_user_stats(user_id):
    """Get user statistics and prediction history"""
    try:
        user = User.get_user_by_id(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        predictions = Prediction.get_user_predictions(user_id)
        
        return jsonify({
            'user': user,
            'predictions': predictions
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Admin Authentication Route

@app.route('/api/admin/login', methods=['POST'])
def admin_login():
    """Admin login endpoint"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400

        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            # Return a simple session token
            return jsonify({
                'message': 'Login successful',
                'token': 'admin_session_token',
                'username': username
            }), 200
        else:
            return jsonify({'error': 'Invalid credentials'}), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Admin API Routes

@app.route('/api/admin/create_match', methods=['POST'])
@admin_required
def create_match():
    """Create a new match (Admin only)"""
    try:
        data = request.get_json()
        team_a = data.get('teamA')
        team_b = data.get('teamB')
        prediction_duration = data.get('prediction_duration_minutes', 5)
        
        if not all([team_a, team_b]):
            return jsonify({'error': 'Missing team names'}), 400
        
        match_id = Match.create_match(team_a, team_b, prediction_duration)
        
        return jsonify({
            'message': 'Match created successfully',
            'match_id': match_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/start_prediction', methods=['POST'])
@admin_required
def start_prediction():
    """Start prediction window for a match (Admin only)"""
    try:
        data = request.get_json()
        match_id = data.get('match_id')
        
        if not match_id:
            return jsonify({'error': 'Missing match_id'}), 400
        
        success = Match.start_prediction(match_id)
        if not success:
            return jsonify({'error': 'Failed to start prediction or match not found'}), 400
        
        return jsonify({'message': 'Prediction started successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/close_prediction', methods=['POST'])
@admin_required
def close_prediction():
    """Close prediction window for a match (Admin only)"""
    try:
        data = request.get_json()
        match_id = data.get('match_id')
        
        if not match_id:
            return jsonify({'error': 'Missing match_id'}), 400
        
        success = Match.close_prediction(match_id)
        if not success:
            return jsonify({'error': 'Failed to close prediction or match not found'}), 400
        
        return jsonify({'message': 'Prediction closed successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/set_winner', methods=['POST'])
@admin_required
def set_winner():
    """Set the winner of a match and award points (Admin only)"""
    try:
        data = request.get_json()
        match_id = data.get('match_id')
        winner_team = data.get('winner_team')
        
        if not all([match_id, winner_team]):
            return jsonify({'error': 'Missing match_id or winner_team'}), 400
        
        # Set winner and award points
        points_awarded = Match.set_winner_and_award_points(match_id, winner_team)
        
        return jsonify({
            'message': 'Winner set and points awarded successfully',
            'points_awarded_to': points_awarded
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/matches', methods=['GET'])
@admin_required
def get_admin_matches():
    """Get all matches with admin details"""
    try:
        matches = Match.get_all_matches_admin()
        return jsonify({'matches': matches}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()}), 200

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
