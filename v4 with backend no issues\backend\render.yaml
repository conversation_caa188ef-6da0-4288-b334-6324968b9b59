services:
  - type: web
    name: tournament-management-api
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: MONGODB_URL
        value: mongodb+srv://truckkun:<EMAIL>/?retryWrites=true&w=majority
      - key: DATABASE_NAME
        value: tournament_management
      - key: SECRET_KEY
        generateValue: true
      - key: ADMIN_USERNAME
        value: admin
      - key: ADMIN_PASSWORD
        value: admin123
